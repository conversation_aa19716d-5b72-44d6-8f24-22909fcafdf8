<template>
  <div class="email-search-wrapper">
    <el-select
      v-model="val"
      filterable
      clearable
      reserve-keyword
      placeholder="输入邮箱搜索用户"
      remote
      :remote-method="search_for_email_user"
      :disabled="disabled"
      v-loading="loading"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)"
      @focus="handle_focus"
      @clear="clear_search_result"
      :style="{ width: width }">

      <el-option-group
        label="搜索结果"
        v-if="search_result.length > 0"
      >
        <el-option v-for="user in search_result"
                   :key="user.id"
                   :label="`${user.id} | ${user.email || ''}`"
                   :value="returnField === 'id' ? user.id : user.email">
        </el-option>
      </el-option-group>

      <el-option-group
        label="历史搜索"
        v-if="email_search_history.length > 0"
      >
        <el-option v-for="user in email_search_history"
                   :key="user.id"
                   :label="`${user.id} | ${user.email || ''}`"
                   :value="returnField === 'id' ? user.id : user.email">
        </el-option>
      </el-option-group>

    </el-select>
  </div>
</template>

<script>
export default {
  name: "EmailSearch",
  props: {
    value: {
      required: false,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '200px'
    },
    storageKey: {
      type: String,
      default: 'email_search_history'
    },
    url: {
      type: String,
      required: true
    },
    returnField: {
      type: String,
      default: 'email', // 'email' 或 'id'
      validator: function (value) {
        return ['email', 'id'].indexOf(value) !== -1
      }
    }
  },
  computed: {
    val: {
      get() {
        return this.value
      },
      set(val) {
        if(val === undefined || val === ''){
          val = null;
        }
        if (val) {
          this.update_search_history(val);
        }
        this.$emit("input", val)
        this.$emit('change', val);
      }
    }
  },
  mounted() {
    this.load_search_history()
  },
  methods: {
    search_for_email_user(email) {
      if (!email) {
        this.search_result = [];
        return;
      }
      this.loading = true;
      let __id = Math.random().toString();
      this.__search_id = __id;
      this.$axios.get(this.url, {params: {email: email}}).then(res => {
        if (__id !== this.__search_id) {
          return;
        }
        if (res?.data?.code === 0) {
          // 只显示有邮箱的用户
          this.search_result = res.data.data.items.filter(user => user.email);
        } else {
          this.search_result = [];
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    clear_search_result() {
      this.search_result = [];
    },
    handle_focus() {
      this.email_search_history = this.get_search_history();
    },
    update_search_history(value) {
      let user;
      if (this.returnField === 'id') {
        user = this.search_result.find(u => u.id === value);
      } else {
        user = this.search_result.find(u => u.email === value);
      }
      if (user) {
        this.insert_into_history(user);
      }
    },
    insert_into_history(user) {
      let history = this.get_search_history();
      let existingIndex = history.findIndex(item => item.id === user.id);
      
      if (existingIndex !== -1) {
        // 如果已存在，移到最前面
        history.splice(existingIndex, 1);
      }
      
      history.unshift(user);
      this.set_search_history(history);
      this.email_search_history = history;
    },
    load_search_history(){
      this.email_search_history = this.get_search_history()
    },
    get_search_history() {
      let ret = localStorage.getItem(this.storageKey);
      if (ret) {
        return JSON.parse(ret);
      }
      return [];
    },
    set_search_history(val) {
      let slice_val = val.slice(0, 10); // 只保存最近10条
      localStorage.setItem(this.storageKey, JSON.stringify(slice_val));
    }
  },
  data() {
    return {
      search_result: [],
      email_search_history: [],
      loading: false
    }
  }
}
</script>

<style scoped>
.email-search-wrapper {
  position: relative;
  display: inline-block;
}
</style>
