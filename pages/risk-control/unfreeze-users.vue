<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      风控>自助解冻管理
    </h2>

    <el-form :inline="true">
      <el-form-item label="状态">
        <el-select v-model="filters.status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in statuses"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型">
        <el-select v-model="filters.identity_type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in identity_types"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="身份认证">
        <el-select v-model="filters.kyc_status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option value="PASSED" label="已认证"></el-option>
          <el-option value="NONE" label="未认证"></el-option>
          <el-option value="PROCESSING" label="处理中"></el-option>
          <el-option value="FAILED" label="失败"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="提交时资产">
        <el-select v-model="filters.balance_choice"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in balance_choices"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-col :span="11">
          <el-date-picker
            v-model="filters.start_date"
            type="datetime"
            value-format="timestamp"
            :picker-options="pickerOptions"
            placeholder="开始时间">
          </el-date-picker>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="filters.end_date"
            type="datetime"
            value-format="timestamp"
            :picker-options="pickerOptions"
            placeholder="结束时间">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="用户">
        <UserSearch v-model="filters.user_id" @change="handle_user_keyword_input"></UserSearch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handle_page_refresh">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              v-loading="loading"
              @sort-change="handle_sort_change"
              stripe>
      <el-table-column label="ID"
                       prop="id"
                       align="left">
      </el-table-column>

      <el-table-column label="用户ID"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{scope.row.user_id}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="邮箱"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.email }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="身份认证"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.kyc_status === 'PASSED'" :href="'/operation/kyc-details?id=' + scope.row.kyc_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ kyc_statuses[scope.row.kyc_status] }}
          </el-link>
          <span v-else>
            {{ kyc_statuses[scope.row.kyc_status] }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="提交时资产"
                       prop="balance_usd">
      </el-table-column>

      <el-table-column label="类型"
                       prop="status"
                       :formatter="row => identity_types[row.identity_type]">
      </el-table-column>

      <el-table-column label="生物识别验证ID"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.liveness_transaction_id" :href="'/operation/liveness-check?transaction_id=' + scope.row.liveness_transaction_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.liveness_id }}
          </el-link>
          <span v-else>
            {{ scope.row.liveness_id }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="生物识别验证结果"
                       prop="liveness_status"
                       :formatter="row => liveness_statuses[row.liveness_status]">
      </el-table-column>

      <el-table-column label="状态"
                       prop="status"
                       :formatter="row => statuses[row.status]">
      </el-table-column>

      <el-table-column label="申请时间"
                       prop="created_at"
                       sortable="custom"
                       :formatter="row => format_date(row.created_at)">
      </el-table-column>

      <el-table-column label="初审时间"
                       :formatter="row => row.audited_at?format_date(row.audited_at):''">
      </el-table-column>

      <el-table-column label="复审时间"
                       :formatter="row => row.checked_at?format_date(row.checked_at):''">
      </el-table-column>

      <!--      <el-table-column label="重置类型"-->
      <!--                       prop="reset_type">-->
      <!--      </el-table-column>-->

      <!--      <el-table-column label="KYC申请时间"-->
      <!--                       show-overflow-tooltip-->
      <!--                       :formatter="row => row.kyc_created_at ? format_date(row.kyc_created_at): '-'">-->
      <!--      </el-table-column>-->

      <el-table-column prop="remark" label="备注"> </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="编辑备注" placement="right" :open-delay="500" :hide-after="2000">
            <el-button size="mini" type="primary" icon="el-icon-edit" circle @click="comment_row(scope.row)"></el-button>
          </el-tooltip>
          <el-link :href=handle_detail_url(scope)
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            <el-tooltip content="去审核" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-more" circle></el-button>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="handle_limit_change"
                   @current-change="handle_page_change"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
import moment from "moment";
import UserSearch from "~/components/user/UserSearch.vue";

export default {
  components: {
    UserSearch
  },
  methods: {
    get_data() {
      this.loading = true;
      this.$axios.get('/api/operation/unfreeze', {params: this.filters}).then(
        res => {
          this.loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            this.statuses = data.statuses;
            this.liveness_statuses = data.liveness_statuses;
            this.identity_types = data.identity_types;
            this.balance_choices = data.balance_choices;
            this.pass_types = data.pass_types;
            this.kyc_statuses = data.kyc_statuses;
          } else {
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    handle_sort_change({ column, prop, order }) {
      this.filters.order = prop;
      if (order === 'descending') {
        this.filters.order_type = 'desc';
      } else if (order === 'ascending') {
        this.filters.order_type = 'asc';
      } else {
        this.filters.order_type = null;
      }
      this.handle_page_refresh();
    },
    comment_row(row) {
      this.$prompt('请输入备注', '', {
        inputValue: row.remark,
        closeOnClickModal: false,
      }).then(({ value }) => {
        this.edit_remark(value, row.id);
      });
    },
    edit_remark(remark, id) {
      let remark_url = `/api/operation/unfreeze/${id}/remark`
      this.$axios
        .patch(remark_url, { "remark": remark})
        .then(res => {
          if (res?.data?.code === 0) {
            this.get_data();
            this.$message.success('操作成功!');
          } else {
            this.$message.error(`操作失败! (code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data});`);
          }
        })
        .catch(err => {
          this.$message.error(`操作失败! (${err})`);
        });
    },
    handle_detail_url(scope){
      let base_url = '/risk-control/unfreeze-detail?application_id=' + scope.row.id
      let paramStr = ''
      let filters = {};
      Object.assign(filters, this.filters);
      Object.keys(filters).forEach(function (key) {
        if(filters[key]) {
          paramStr += `&${key}=${filters[key]}`
        }
      })
      return base_url + paramStr
    },
    handle_status_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_user_keyword_input() {
      this.reset_page();
      this.get_data();
    },
    handle_id_num_input() {
      this.reset_page();
      this.get_data();
    },
    handle_date_range_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach((key) => !query[key] && delete query[key]);
      this.$router.replace({query: query});
    }
  },
  created() {
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        status: "CREATED",
        kyc_status: null,
        balance_choice: null,
        identity_type: null,
        pass_type: null,
        start_date: null,
        end_date: null,
        user_id: null,
        page: null,
        limit: 50
      },
      items: [],
      total: 0,
      statuses: {},
      liveness_statuses: {},
      identity_types: {},
      loading: true,
      pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        },
    }
  },
}
</script>
